"""Test suite for MLP MNIST model, data loading, and training/validation logic."""

# pylint: disable=redefined-outer-name, reimported, import-outside-toplevel
# Disable warnings for test isolation - test functions require local imports for independence

# Group and order imports: standard library, third-party, then local imports.
# Alphabetize within each group. No imports inside functions/classes.
import os
import random
from dataclasses import dataclass

import numpy as np
import pytest
import torch
from torch import optim

from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms


from mnist_train import (
    SimpleMNISTModel,
    evaluate_model,
    save_checkpoint,
    load_latest_checkpoint,
    PatchedMNIST,
)

random.seed(42)
torch.manual_seed(42)
np.random.seed(42)


def get_small_mnist_loaders(batch_size: int = 8, num_samples: int = 100) -> tuple:
    """
    Get small MNIST DataLoaders for testing.
    Args:
        batch_size (int): Batch size for loaders.
        num_samples (int): Number of samples for train/test.
    Returns:
        tuple: (train_loader, test_loader)
    Raises:
        AssertionError: If batch size is incorrect.
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    train_dataset = datasets.MNIST(
        root="./mnist_data",
        train=True,
        download=True,
        transform=transform,
    )
    test_dataset = datasets.MNIST(
        root="./mnist_data",
        train=False,
        download=True,
        transform=transform,
    )
    train_dataset = Subset(train_dataset, range(num_samples))
    test_dataset = Subset(test_dataset, range(num_samples))
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, test_loader


def test_mnist_download_and_load(tmp_path):
    """
    Test that the MNIST dataset can be downloaded and loaded correctly (small subset).
    Args:
        tmp_path (Path): Temporary path for dataset download.
    Returns:
        None
    Raises:
        AssertionError: If dataset sizes or sample shape are incorrect.
    """
    train_loader, test_loader = get_small_mnist_loaders(batch_size=8, num_samples=32)
    # Check dataset sizes
    assert len(train_loader.dataset) == 32, "Training set should have 32 samples."
    assert len(test_loader.dataset) == 32, "Test set should have 32 samples."
    # Check sample shape
    x, y = next(iter(train_loader))
    assert x.shape[1:] == (1, 28, 28), "Each image should be 1x28x28."
    assert isinstance(y[0].item(), int), "Label should be an integer."


def test_simple_mnist_model_output_shape():
    """
    Test that the model produces the correct output shape for a batch of MNIST images.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If output shape is incorrect.
    """
    model = SimpleMNISTModel()
    x = torch.randn(8, 1, 28, 28)
    output = model(x)
    assert output.shape == (8, 10), f"Expected output shape (8, 10), got {output.shape}"


@pytest.mark.skipif(
    not hasattr(torch, "compile"),
    reason="torch.compile requires PyTorch 2.x+",
)
def test_simple_mnist_model_torch_compile_output_shape():
    """Test that torch.compile model produces correct output shape."""
    model = SimpleMNISTModel()
    # Use eager backend on Windows to avoid C++ compilation issues
    import platform

    backend = "eager" if platform.system() == "Windows" else "inductor"
    compiled_model = torch.compile(model, backend=backend)
    x = torch.randn(8, 1, 28, 28)
    output = compiled_model(x)
    assert output.shape == (8, 10)


def test_training_step_decreases_loss():
    """
    Test that a single training step decreases the loss (or at least runs without error).
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If loss did not decrease or exploded.
    """

    train_loader, _ = get_small_mnist_loaders(batch_size=8, num_samples=32)
    model = SimpleMNISTModel()
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    model.train()
    images, labels = next(iter(train_loader))
    outputs = model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"


@pytest.mark.skipif(
    not hasattr(torch, "compile"),
    reason="torch.compile requires PyTorch 2.x+",
)
def test_simple_mnist_model_torch_compile_training_step():
    """Test that torch.compile model can perform training step."""

    train_loader, _ = get_small_mnist_loaders(batch_size=8, num_samples=32)
    model = SimpleMNISTModel()
    compiled_model = torch.compile(model)
    optimizer = optim.SGD(compiled_model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    compiled_model.train()
    images, labels = next(iter(train_loader))
    outputs = compiled_model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = compiled_model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"


def test_evaluate_model_accuracy():
    """
    Test that the evaluation function returns accuracy in [0, 1] for a random model.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If accuracy is outside [0, 1].
    """
    _, test_loader = get_small_mnist_loaders(batch_size=8, num_samples=32)
    model = SimpleMNISTModel()
    acc = evaluate_model(model, test_loader)
    assert 0.0 <= acc <= 1.0, f"Accuracy should be between 0 and 1, got {acc}"


def test_checkpoint_save_and_load(
    tmp_path,
):  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test that saving and loading a checkpoint restores model and optimizer state.
    Args:
        tmp_path (Path): Temporary path for checkpoint saving/loading.
    Returns:
        None
    Raises:
        AssertionError: If model weights or optimizer state are not restored correctly.
    """
    import torch  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    import torch.optim as optim  # pylint: disable=consider-using-from-import,import-outside-toplevel

    model = SimpleMNISTModel()
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    for param in model.parameters():
        param.data.fill_(1.0)
    save_checkpoint(model, optimizer, 3, checkpoint_dir=tmp_path)
    for param in model.parameters():
        param.data.fill_(0.0)
    epoch = load_latest_checkpoint(model, optimizer, checkpoint_dir=tmp_path)
    for param in model.parameters():
        assert torch.all(
            param.data == 1.0
        ), "Model weights not restored from checkpoint."
    assert epoch == 4, "Should resume from next epoch."


def test_periodic_checkpoint_saving(
    tmp_path,
):  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test that periodic checkpoint saving works (simulate by forcing time).
    Args:
        tmp_path (Path): Temporary path for checkpoint saving.
    Returns:
        None
    Raises:
        AssertionError: If periodic checkpoints are not saved correctly.
    """
    from mnist_train import (
        SimpleMNISTModel,
        train_model,
    )  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    train_loader, test_loader = get_small_mnist_loaders(batch_size=8, num_samples=32)
    model = SimpleMNISTModel()
    import time as time_module  # pylint: disable=import-outside-toplevel

    original_time = time_module.time
    times = [original_time(), original_time() + 61, original_time() + 122]

    def fake_time():
        return times.pop(0) if times else original_time()

    time_module.time = fake_time
    try:
        train_model(
            model,
            train_loader,
            test_loader,
            epochs=1,
            checkpoint_dir=tmp_path,
            save_every_minutes=1,
        )
        ckpts = [f for f in os.listdir(tmp_path) if "batch" in f and f.endswith(".pt")]
        assert (
            len(ckpts) >= 2
        ), "Should save multiple unique periodic checkpoints when save_every_minutes is set."
    finally:
        time_module.time = original_time


def test_evaluate_model_empty_loader():  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test that evaluate_model returns 0.0 for an empty dataloader (cover total==0 branch).
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If accuracy is not 0.0 for an empty loader.
    """
    from torch.utils.data import (
        DataLoader,
    )  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    model = SimpleMNISTModel()
    # Mock dataloader with empty subset to test
    empty_dataset = datasets.MNIST(root="./mnist_data", train=False, download=False)
    empty_loader = DataLoader(Subset(empty_dataset, range(0)))
    acc = evaluate_model(model, empty_loader)
    assert acc == 0.0


def test_train_main_args_none(monkeypatch):  # pylint: disable=import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test train_main with args=None to cover CLI parsing block.
    Args:
        monkeypatch (MonkeyPatch): pytest fixture for modifying sys.argv.
    Returns:
        None
    Raises:
        AssertionError: If train_main does not run without error.
    """
    import sys  # pylint: disable=import-outside-toplevel

    from mnist_train import train_main  # pylint: disable=import-outside-toplevel

    monkeypatch.setattr(
        sys, "argv", ["mnist_train.py", "--minutes", "0.01", "--test-mode"]
    )
    train_main(args=None)


def test_patchedmnist_no_transform():  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test PatchedMNIST with no transform or target_transform.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If PatchedMNIST does not return correct image and target.
    """
    import torch  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    # Create a minimal fake dataset
    class DummyMNIST(PatchedMNIST):
        """Dummy MNIST dataset for testing."""

        def __init__(self):
            super().__init__(root="./mnist_data", train=True, download=True)
            self.data = torch.randint(0, 255, (1, 28, 28), dtype=torch.uint8)
            self.targets = torch.tensor([3])
            self.transform = None
            self.target_transform = None

    ds = DummyMNIST()
    img, target = ds[0]
    assert hasattr(img, "convert") or hasattr(img, "size")  # PIL Image
    assert target == 3


def test_patchedmnist_with_transform_and_target_transform():  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test PatchedMNIST __getitem__ with both transform and target_transform set.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If PatchedMNIST does not return correct image and target.
    """
    import torch  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    class DummyMNIST(PatchedMNIST):
        """Dummy MNIST dataset with transforms for testing."""

        def __init__(self):
            super().__init__(root="./mnist_data", train=True, download=True)
            self.data = torch.randint(0, 255, (1, 28, 28), dtype=torch.uint8)
            self.targets = torch.tensor([3])
            self.transform = lambda x: x.rotate(90)  # PIL Image transform
            self.target_transform = lambda y: y + 1

    ds = DummyMNIST()
    img, target = ds[0]
    assert hasattr(img, "rotate")
    assert target == 4


def test_get_mnist_dataloaders_custom_batch():  # pylint: disable=import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test get_mnist_dataloaders with a custom batch size.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If batch size is incorrect.
    """
    from mnist_train import (
        get_mnist_dataloaders,
    )  # pylint: disable=import-outside-toplevel

    train_loader, _ = get_mnist_dataloaders(batch_size=7)
    batch = next(iter(train_loader))
    assert batch[0].shape[0] == 7


def test_load_latest_checkpoint_no_ckpt(
    tmp_path, monkeypatch
):  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test load_latest_checkpoint returns 1 and logs info if no checkpoint exists.
    Args:
        tmp_path (Path): Temporary path for checkpoint saving.
        monkeypatch (MonkeyPatch): pytest fixture for modifying sys.argv.
    Returns:
        None
    Raises:
        AssertionError: If start_epoch is not 1 or if log message is not present.
    """
    import logging  # pylint: disable=import-outside-toplevel

    import torch.optim as optim  # pylint: disable=consider-using-from-import,import-outside-toplevel

    from mnist_train import (
        SimpleMNISTModel,
        load_latest_checkpoint,
    )  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    model = SimpleMNISTModel()
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    # Patch logger to capture output
    records = []

    class Handler(logging.Handler):
        """Custom logging handler for capturing log records."""

        def emit(self, record):
            records.append(record)

    logger = logging.getLogger("mnist_train")
    handler = Handler()
    logger.setLevel(logging.INFO)  # Ensure INFO logs are emitted
    handler.setLevel(logging.INFO)  # Ensure handler captures INFO logs
    logger.addHandler(handler)
    start_epoch = load_latest_checkpoint(model, optimizer, checkpoint_dir=str(tmp_path))
    logger.removeHandler(handler)
    assert start_epoch == 1
    assert any(
        "No checkpoint found. Starting from scratch." in r.getMessage() for r in records
    )


def test_infer_patchedmnist_transform_branches():  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test PatchedMNIST from mnist_infer with and without transform/target_transform.
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If PatchedMNIST does not return correct image and target.
    """
    from mnist_infer import (
        PatchedMNIST,
    )  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    # No transform/target_transform
    ds = PatchedMNIST(root="./mnist_data", train=False, download=True)
    ds.transform = None
    ds.target_transform = None
    img, target = ds[0]
    assert hasattr(img, "convert")
    # Only transform
    ds.transform = lambda x: x.rotate(90)
    ds.target_transform = None
    img, target = ds[0]
    assert hasattr(img, "rotate")
    # Only target_transform
    ds.transform = None
    ds.target_transform = lambda y: y + 1
    img, target = ds[0]
    assert target == int(ds.targets[0]) + 1
    # Both
    ds.transform = lambda x: x.transpose(0)
    ds.target_transform = lambda y: y + 2
    img, target = ds[0]
    assert hasattr(img, "transpose")
    assert target == int(ds.targets[0]) + 2


def test_evaluate_model_total_zero():  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test evaluate_model from mnist_train for total==0 branch (empty loader).
    Args:
        None
    Returns:
        None
    Raises:
        AssertionError: If accuracy is not 0.0 for an empty loader.
    """
    from torch.utils.data import (
        DataLoader,
    )  # pylint: disable=redefined-outer-name,reimported,import-outside-toplevel

    model = SimpleMNISTModel()
    empty_dataset = datasets.MNIST(root="./mnist_data", train=False, download=False)
    empty_loader = DataLoader(Subset(empty_dataset, range(0)))
    acc = evaluate_model(model, empty_loader)
    assert acc == 0.0


def test_validate_main_model_none(
    monkeypatch,
):  # pylint: disable=import-outside-toplevel
    # Disable warnings for test isolation - imports inside functions ensure test independence
    """
    Test validate_main from mnist_validate for model=None branch.
    Simulates load_latest_checkpoint returns None.
    Args:
        monkeypatch (MonkeyPatch): pytest fixture for modifying load_latest_checkpoint.
    Returns:
        None
    Raises:
        AssertionError: If validate_main does not return early without error.
    """
    from mnist_validate import validate_main  # pylint: disable=import-outside-toplevel

    monkeypatch.setattr(
        "mnist_validate.load_latest_checkpoint", lambda *args, **kwargs: None
    )

    @dataclass
    class Args:
        """Mock arguments for testing."""

        test_mode: bool = True
        checkpoint_dir: str = "./checkpoints"

    # Should return early without error
    validate_main(Args())
